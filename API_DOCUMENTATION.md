# User Management API Documentation

## Overview
This is a Spring Boot application with JPA that provides a complete user management system following the MVC pattern.

## Features
- Create, Read, Update, Delete (CRUD) operations for users
- List single user and multiple users
- Search functionality
- Input validation
- Global exception handling
- MySQL database integration

## Database Setup
1. Install MySQL
2. Create a database named `jpaproject_db` (or let the application create it automatically)
3. Update the database credentials in `application.properties` if needed

## API Endpoints

### Base URL: `http://localhost:8080/api/users`

### 1. Create User
- **POST** `/api/users`
- **Request Body:**
```json
{
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main St, City, State"
}
```
- **Response:** User object with generated ID and timestamps

### 2. Get All Users
- **GET** `/api/users`
- **Query Parameters:**
  - `sortBy` (optional): `id`, `name`, `created`
- **Response:** List of all users

### 3. Get Single User by ID
- **GET** `/api/users/{id}`
- **Response:** Single user object

### 4. Get User by Email
- **GET** `/api/users/email/{email}`
- **Response:** Single user object

### 5. Update User
- **PUT** `/api/users/{id}`
- **Request Body:** User object with updated fields
- **Response:** Updated user object

### 6. Delete User
- **DELETE** `/api/users/{id}`
- **Response:** Success message

### 7. Search Users
- **GET** `/api/users/search?query={searchTerm}`
- **Response:** List of users matching the search term

### 8. Get Users Count
- **GET** `/api/users/count`
- **Response:** Total number of users

## User Model
```json
{
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main St, City, State",
    "createdAt": "2024-01-01T10:00:00",
    "updatedAt": "2024-01-01T10:00:00"
}
```

## Validation Rules
- **Name**: Required, 2-50 characters
- **Email**: Required, valid email format, unique
- **Phone**: 10-15 characters, unique (optional)
- **Address**: Max 100 characters (optional)

## Error Responses
All error responses follow this format:
```json
{
    "success": false,
    "message": "Error description",
    "timestamp": "2024-01-01T10:00:00",
    "errors": {} // For validation errors
}
```

## Running the Application
1. Ensure MySQL is running
2. Update database credentials in `application.properties`
3. Run: `mvn spring-boot:run`
4. Access API at: `http://localhost:8080/api/users`

## Testing with cURL

### Create User:
```bash
curl -X POST http://localhost:8080/api/users \
  -H "Content-Type: application/json" \
  -d '{"name":"John Doe","email":"<EMAIL>","phone":"1234567890"}'
```

### Get All Users:
```bash
curl http://localhost:8080/api/users
```

### Get User by ID:
```bash
curl http://localhost:8080/api/users/1
```

### Update User:
```bash
curl -X PUT http://localhost:8080/api/users/1 \
  -H "Content-Type: application/json" \
  -d '{"name":"John Smith","email":"<EMAIL>","phone":"0987654321"}'
```

### Delete User:
```bash
curl -X DELETE http://localhost:8080/api/users/1
```
