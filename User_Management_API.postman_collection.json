{"info": {"_postman_id": "user-management-api", "name": "User Management API", "description": "Collection for testing User Management Spring Boot API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Create User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Test User\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"1234567890\",\n    \"address\": \"123 Test Street\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}}, {"name": "Get All Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users", "host": ["{{baseUrl}}"], "path": ["api", "users"]}}}, {"name": "Get All Users Sorted by Name", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users?sortBy=name", "host": ["{{baseUrl}}"], "path": ["api", "users"], "query": [{"key": "sortBy", "value": "name"}]}}}, {"name": "Get User by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/1", "host": ["{{baseUrl}}"], "path": ["api", "users", "1"]}}}, {"name": "Get User by Email", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/email/<EMAIL>", "host": ["{{baseUrl}}"], "path": ["api", "users", "email", "<EMAIL>"]}}}, {"name": "Update User", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated User\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"9876543210\",\n    \"address\": \"456 Updated Street\"\n}"}, "url": {"raw": "{{baseUrl}}/api/users/1", "host": ["{{baseUrl}}"], "path": ["api", "users", "1"]}}}, {"name": "Delete User", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/users/1", "host": ["{{baseUrl}}"], "path": ["api", "users", "1"]}}}, {"name": "Search Users", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/search?query=john", "host": ["{{baseUrl}}"], "path": ["api", "users", "search"], "query": [{"key": "query", "value": "john"}]}}}, {"name": "Get Users Count", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/users/count", "host": ["{{baseUrl}}"], "path": ["api", "users", "count"]}}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080"}]}