# User Management Spring Boot Application

A complete Spring Boot application with JPA for managing user profiles. This application provides a RESTful API for CRUD operations on user data.

## Features

- ✅ Create new users
- ✅ Retrieve single user by ID or email
- ✅ List all users with sorting options
- ✅ Update existing users
- ✅ Delete users
- ✅ Search users by name or email
- ✅ Input validation
- ✅ Global exception handling
- ✅ MySQL database integration

## Technology Stack

- **Java 21**
- **Spring Boot 3.5.6**
- **Spring Data JPA**
- **MySQL Database**
- **Maven**
- **Bean Validation**

## Prerequisites

- Java 21 or higher
- MySQL 8.0 or higher
- Maven 3.6 or higher

## Database Setup

1. Install MySQL and create a database:
```sql
CREATE DATABASE jpaproject_db;
```

2. Update database credentials in `src/main/resources/application.properties`:
```properties
spring.datasource.username=your_username
spring.datasource.password=your_password
```

## Running the Application

1. Clone the repository
2. Navigate to the project directory
3. Run the application:
```bash
mvn spring-boot:run
```

The application will start on `http://localhost:8080`

## API Endpoints

### Base URL: `http://localhost:8080/api/users`

### 1. Create User
- **POST** `/api/users`
- **Body:**
```json
{
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main Street"
}
```

### 2. Get All Users
- **GET** `/api/users`
- **Query Parameters:**
  - `sortBy`: `id` (default), `name`, `created`

### 3. Get User by ID
- **GET** `/api/users/{id}`

### 4. Get User by Email
- **GET** `/api/users/email/{email}`

### 5. Update User
- **PUT** `/api/users/{id}`
- **Body:** Same as create user

### 6. Delete User
- **DELETE** `/api/users/{id}`

### 7. Search Users
- **GET** `/api/users/search?query={searchTerm}`

### 8. Get Users Count
- **GET** `/api/users/count`

## Response Format

All API responses follow this format:
```json
{
    "success": true/false,
    "message": "Description of the result",
    "data": {}, // Response data (when applicable)
    "count": 0, // Count (when applicable)
    "timestamp": "2024-01-01T12:00:00" // Error timestamp (when applicable)
}
```

## User Model

```json
{
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "phone": "1234567890",
    "address": "123 Main Street",
    "createdAt": "2024-01-01T12:00:00",
    "updatedAt": "2024-01-01T12:00:00"
}
```

## Validation Rules

- **Name**: Required, 2-50 characters
- **Email**: Required, valid email format, unique
- **Phone**: 10-15 characters, unique (optional)
- **Address**: Max 100 characters (optional)

## Testing

Run tests with:
```bash
mvn test
```

## Project Structure

```
src/
├── main/
│   ├── java/com/example/JPAPROJECT/
│   │   ├── CONTROLLRES/
│   │   │   └── UserController.java
│   │   ├── SERVICE/
│   │   │   ├── UserService.java
│   │   │   └── UserServiceImpl.java
│   │   ├── REPOSITORY/
│   │   │   └── UserRepository.java
│   │   ├── MODEL/
│   │   │   └── User.java
│   │   ├── EXCEPTION/
│   │   │   ├── UserNotFoundException.java
│   │   │   └── GlobalExceptionHandler.java
│   │   └── JpaprojectApplication.java
│   └── resources/
│       └── application.properties
└── test/
    └── java/com/example/JPAPROJECT/
        └── UserControllerTest.java
```
