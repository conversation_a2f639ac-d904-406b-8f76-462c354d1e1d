package com.example.JPAPROJECT.REPOSITORY;

import com.example.JPAPROJECT.MODEL.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
    
    // Find user by email
    Optional<User> findByEmail(String email);
    
    // Find users by name containing (case insensitive)
    List<User> findByNameContainingIgnoreCase(String name);
    
    // Find users by phone
    Optional<User> findByPhone(String phone);
    
    // Check if email exists
    boolean existsByEmail(String email);
    
    // Check if phone exists
    boolean existsByPhone(String phone);
    
    // Custom query to find users by name or email
    @Query("SELECT u FROM User u WHERE u.name LIKE %:searchTerm% OR u.email LIKE %:searchTerm%")
    List<User> findByNameOrEmailContaining(@Param("searchTerm") String searchTerm);
    
    // Find all users ordered by name
    @Query("SELECT u FROM User u ORDER BY u.name ASC")
    List<User> findAllOrderByName();
    
    // Find all users ordered by creation date (newest first)
    @Query("SELECT u FROM User u ORDER BY u.createdAt DESC")
    List<User> findAllOrderByCreatedAtDesc();
}
