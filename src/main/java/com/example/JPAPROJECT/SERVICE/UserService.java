package com.example.JPAPROJECT.SERVICE;

import com.example.JPAPROJECT.MODEL.User;
import java.util.List;
import java.util.Optional;

public interface UserService {
    
    // Create a new user
    User createUser(User user);
    
    // Get user by ID
    Optional<User> getUserById(Long id);
    
    // Get user by email
    Optional<User> getUserByEmail(String email);
    
    // Get all users
    List<User> getAllUsers();
    
    // Get all users ordered by name
    List<User> getAllUsersOrderByName();
    
    // Get all users ordered by creation date
    List<User> getAllUsersOrderByCreatedDate();
    
    // Update user
    User updateUser(Long id, User userDetails);
    
    // Delete user by ID
    void deleteUser(Long id);
    
    // Search users by name or email
    List<User> searchUsers(String searchTerm);
    
    // Check if email exists
    boolean emailExists(String email);
    
    // Check if phone exists
    boolean phoneExists(String phone);
    
    // Get users count
    long getUsersCount();
}
