package com.example.JPAPROJECT.SERVICE;

import com.example.JPAPROJECT.MODEL.User;
import java.util.List;
import java.util.Optional;

public interface UserService {
    
    User createUser(User user);
    
    Optional<User> getUserById(Long id);
    
    Optional<User> getUserByEmail(String email);
    
    List<User> getAllUsers();
    
    List<User> getAllUsersOrderByName();
    
    List<User> getAllUsersOrderByCreatedDate();
    
    User updateUser(Long id, User userDetails);
    
    void deleteUser(Long id);
    
    List<User> searchUsers(String searchTerm);
    
    boolean emailExists(String email);
    
    boolean phoneExists(String phone);
    
    long getUsersCount();
}
