package com.example.JPAPROJECT.SERVICE;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.example.JPAPROJECT.MODEL.User;
import com.example.JPAPROJECT.REPOSITORY.UserRepository;
import com.example.JPAPROJECT.CONTROLLRES.UserController;
import com.example.JPAPROJECT.EXCEPTION.UserNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserServiceImpl implements UserService {
        private static final Logger log = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserRepository userRepository;
    
    @Override
    public User createUser(User user) {
        log.info("Received request to create user: {}", user.getName());

        // Check if email already exists 
        if (userRepository.existsByEmail(user.getEmail())) {
            throw new RuntimeException("Email already exists: " + user.getEmail());
        }
        
        // Check if phone already exists (if phone is provided)
        if (user.getPhone() != null && !user.getPhone().isEmpty() && 
            userRepository.existsByPhone(user.getPhone())) {
            throw new RuntimeException("Phone number already exists: " + user.getPhone());
        }
        
        return userRepository.save(user);     
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<User> getUserById(Long id) {
        return userRepository.findById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public Optional<User> getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getAllUsersOrderByName() {
        return userRepository.findAllOrderByName();
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> getAllUsersOrderByCreatedDate() {
        return userRepository.findAllOrderByCreatedAtDesc();
    }
    
    @Override
    public User updateUser(Long id, User userDetails) {
        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException(id));
        
        // Check if email is being changed and if new email already exists
        if (!user.getEmail().equals(userDetails.getEmail()) && 
            userRepository.existsByEmail(userDetails.getEmail())) {
            throw new RuntimeException("Email already exists: " + userDetails.getEmail());
        }
        
        // Check if phone is being changed and if new phone already exists
        if (userDetails.getPhone() != null && !userDetails.getPhone().isEmpty() &&
            !userDetails.getPhone().equals(user.getPhone()) &&
            userRepository.existsByPhone(userDetails.getPhone())) {
            throw new RuntimeException("Phone number already exists: " + userDetails.getPhone());
        }
        
        // Update user fields
        user.setName(userDetails.getName());
        user.setEmail(userDetails.getEmail());
        user.setPhone(userDetails.getPhone());
        user.setAddress(userDetails.getAddress());
        
        return userRepository.save(user);
    }
    
    @Override
    public void deleteUser(Long id) { 
        log.info("Received request to delete user with id: {}", id);
        User user = userRepository.findById(id)
                .orElseThrow(() -> new UserNotFoundException(id));
        userRepository.delete(user);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<User> searchUsers(String searchTerm) {  
        log.info("Received request to search users for term: {}", searchTerm);
        return userRepository.findByNameOrEmailContaining(searchTerm);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean emailExists(String email) {
        return userRepository.existsByEmail(email);
    }
    
    @Override
    @Transactional(readOnly = true)
    public boolean phoneExists(String phone) {
        return userRepository.existsByPhone(phone);
    }
    
    @Override
    @Transactional(readOnly = true)
    public long getUsersCount() {
        return userRepository.count();
    }
}
