// package com.example.JPAPROJECT.config;

// import io.swagger.v3.oas.models.OpenAPI;
// import io.swagger.v3.oas.models.info.Contact;
// import io.swagger.v3.oas.models.info.Info;
// import io.swagger.v3.oas.models.info.License;
// import io.swagger.v3.oas.models.servers.Server;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;

// import java.util.List;

// @Configuration
// public class SwaggerConfig {

//     @Bean
//     public OpenAPI customOpenAPI() {
//         return new OpenAPI()
//                 .info(new Info()
//                         .title("User Management API")
//                         .description("A comprehensive REST API for managing user profiles with full CRUD operations")
//                         .version("1.0.0")
//                         .contact(new Contact()
//                                 .name("API Support")
//                                 .email("<EMAIL>")
//                                 .url("https://example.com"))
//                         .license(new License()
//                                 .name("MIT License")
//                                 .url("https://opensource.org/licenses/MIT")))
//                 .servers(List.of(
//                         new Server()
//                                 .url("http://localhost:8081")
//                                 .description("Development Server"),
//                         new Server()
//                                 .url("https://api.example.com")
//                                 .description("Production Server")
//                 ));
//     }
// }
