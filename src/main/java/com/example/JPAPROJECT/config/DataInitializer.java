// package com.example.JPAPROJECT.config;

// import com.example.JPAPROJECT.MODEL.User;
// import com.example.JPAPROJECT.REPOSITORY.UserRepository;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.CommandLineRunner;
// import org.springframework.stereotype.Component;


// @Component
// public class DataInitializer implements CommandLineRunner {

//     @Autowired
//     private UserRepository userRepository;

//     @Override
//     public void run(String... args) throws Exception {
//         if (userRepository.count() == 0) {
//             initializeSampleData();
//         }
//     }

//     private void initializeSampleData() {
//         System.out.println("Initializing sample data...");

       
//         User user1 = new User("John Doe", "<EMAIL>", "1234567890", "123 Main Street, New York");
//         User user2 = new User("<PERSON>", "<EMAIL>", "0987654321", "456 Oak Avenue, Los Angeles");
//         User user3 = new User("<PERSON>", "<EMAIL>", "5555555555", "789 Pine Road, Chicago");
//         User user4 = new User("Sarah Wilson", "<EMAIL>", "1111111111", "321 Elm Street, Houston");
//         User user5 = new User("David Brown", "<EMAIL>", "2222222222", "654 Maple Drive, Phoenix");

        
//         userRepository.save(user1);
//         userRepository.save(user2);
//         userRepository.save(user3);
//         userRepository.save(user4);
//         userRepository.save(user5);

//         System.out.println("Sample data initialized successfully!");
//         System.out.println("Created " + userRepository.count() + " sample users.");
//     }
// }
