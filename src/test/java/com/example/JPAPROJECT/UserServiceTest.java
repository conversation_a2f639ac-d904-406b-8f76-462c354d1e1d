package com.example.JPAPROJECT;

import com.example.JPAPROJECT.MODEL.User;
import com.example.JPAPROJECT.SERVICE.UserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect"
})
@Transactional
public class UserServiceTest {

    @Autowired
    private UserService userService;

    @Test
    public void testCreateUser() {
        User user = new User("Test User", "<EMAIL>", "1234567890", "123 Test St");
        
        User savedUser = userService.createUser(user);
        
        assertNotNull(savedUser);
        assertNotNull(savedUser.getId());
        assertEquals("Test User", savedUser.getName());
        assertEquals("<EMAIL>", savedUser.getEmail());
    }

    @Test
    public void testGetAllUsers() {
        User user1 = new User("User 1", "<EMAIL>", "1111111111", "Address 1");
        User user2 = new User("User 2", "<EMAIL>", "2222222222", "Address 2");
        
        userService.createUser(user1);
        userService.createUser(user2);
        
        var users = userService.getAllUsers();
        
        assertTrue(users.size() >= 2);
    }

    @Test
    public void testGetUserById() {
        User user = new User("Test User", "<EMAIL>", "1234567890", "123 Test St");
        User savedUser = userService.createUser(user);
        
        var foundUser = userService.getUserById(savedUser.getId());
        
        assertTrue(foundUser.isPresent());
        assertEquals("Test User", foundUser.get().getName());
    }

    @Test
    public void testUpdateUser() {
        User user = new User("Original Name", "<EMAIL>", "1234567890", "Original Address");
        User savedUser = userService.createUser(user);
        
        User updateData = new User("Updated Name", "<EMAIL>", "0987654321", "Updated Address");
        User updatedUser = userService.updateUser(savedUser.getId(), updateData);
        
        assertEquals("Updated Name", updatedUser.getName());
        assertEquals("<EMAIL>", updatedUser.getEmail());
    }

    @Test
    public void testDeleteUser() {
        User user = new User("Test User", "<EMAIL>", "1234567890", "123 Test St");
        User savedUser = userService.createUser(user);
        
        userService.deleteUser(savedUser.getId());
        
        var foundUser = userService.getUserById(savedUser.getId());
        assertFalse(foundUser.isPresent());
    }

    @Test
    public void testApplicationContextLoads() {
        assertNotNull(userService);
    }
}
